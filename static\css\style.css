/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 430px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    padding: 20px;
}

/* APP头部信息 */
.app-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.app-icon {
    position: relative;
    margin-right: 16px;
    flex-shrink: 0;
}

.app-icon img {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}



.app-info {
    flex: 1;
    padding-top: 4px;
}

.app-name {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.app-rating {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 12px;
}

.rating-score {
    font-size: 14px;
    color: #666;
}

.download-count {
    font-size: 14px;
    color: #666;
}

/* APP元信息 */
.app-meta-info {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.app-category,
.app-version,
.app-size,
.app-age {
    display: inline-block;
    background: #e8f4fd;
    color: #007aff;
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.app-intro {
    margin-top: 8px;
}

.intro-text {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    /* font-style: italic; */
}

/* 下载按钮 */
.download-section {
    margin-bottom: 24px;
}

.download-btn {
    width: 100%;
    background: #007aff;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: #0056cc;
}

.download-btn:active {
    transform: scale(0.98);
}

/* 安全认证区域 */
.security-section {
    margin-bottom: 24px;
}

.security-card {
    background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    border: 1px solid #d4edda;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.08);
}

.security-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.security-shield {
    width: 24px;
    height: 24px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    margin-right: 8px;
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.security-title h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #28a745;
    line-height: 1.2;
}

.security-desc {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.3;
}

.security-features {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    justify-content: center;
}

.feature-tag {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    font-size: 10px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: color 0.3s ease;
}

.tab-item.active {
    color: #007aff;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: #007aff;
    border-radius: 1px;
}

/* 标签页文字容器 */
.tab-text {
    position: relative;
    display: inline-block;
}

/* 评论角标 */
.comment-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff3b30;
    color: white;
    font-size: 9px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 8px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 应用截图 */
.screenshots-container {
    margin-bottom: 32px;
}

.screenshots-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.screenshots-wrapper::-webkit-scrollbar {
    display: none;
}

.screenshot-item {
    flex: 0 0 calc(33.333% - 8px);
    height: 166px;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.screenshot-item:hover {
    transform: scale(1.02);
}

.screenshot-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 应用简介 */
.app-description {
    margin-bottom: 32px;
}

.app-description h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.app-description p {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
}

/* 用户评论 */
.reviews-header {
    margin-bottom: 20px;
}

.reviews-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.reviews-list {
    /* 评论列表样式 */
}

.review-item {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

.review-item:last-child {
    border-bottom: none;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.review-user {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.review-rating {
    font-size: 12px;
    color: #ffa500;
}

.review-text {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
}

/* 图片放大查看模态框 */
.lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.lightbox.active {
    display: flex;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 10px;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0,0,0,0.5);
}

/* 加载提示 */
.loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: white;
}

.loading.active {
    display: flex;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
