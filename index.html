<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歪歪漫画 - 下载页面</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/responsive.css">
</head>
<body>
    <!-- 页面容器 -->
    <div class="container">
        <!-- APP基本信息区域 -->
        <section class="app-header">
            <div class="app-icon">
                <img src="static/images/logo.png" alt="歪歪漫画" id="appLogo">
            </div>
            <div class="app-info">
                <h1 class="app-name">歪歪漫画</h1>
                <div class="app-rating">
                    <span class="rating-score">⭐ 4.8</span>
                    <span class="download-count">570万下载</span>
                </div>
                <div class="app-meta-info">
                    <div class="app-category">漫画阅读</div>
                    <div class="app-version">1天前更新</div>
                    <div class="app-size">4.6MB</div>
                    <div class="app-age">12+</div>
                </div>
                <div class="app-intro">
                    <span class="intro-text">超省流量的漫画阅读神器</span>
                </div>
            </div>
        </section>

        <!-- 下载按钮 -->
        <section class="download-section">
            <button class="download-btn" id="downloadBtn" onclick="handleDownload()">
                立即下载
            </button>
        </section>

        <!-- 安全认证区域 -->
        <section class="security-section">
            <div class="security-card">
                <div class="security-header">
                    <div class="security-shield">✓</div>
                    <div class="security-title">
                        <h4>安全认证</h4>
                    </div>
                </div>
                <p class="security-desc">官方正版 · 安全无毒 · 放心下载</p>
                <div class="security-features">
                    <span class="feature-tag">病毒扫描通过</span>
                    <span class="feature-tag">数字签名验证</span>
                    <span class="feature-tag">权威机构认证</span>
                </div>
            </div>
        </section>

        <!-- 标签页导航 -->
        <section class="tab-navigation">
            <div class="tab-item active" data-tab="preview">预览</div>
            <div class="tab-item" data-tab="reviews">
                <span class="tab-text">评论<span class="comment-badge">789</span></span>
            </div>
        </section>

        <!-- 预览内容 -->
        <section class="tab-content active" id="preview-content">
            <!-- 应用截图 -->
            <div class="screenshots-container">
                <div class="screenshots-wrapper" id="screenshotsWrapper">
                    <div class="screenshot-item">
                        <img src="static/images/1.png" alt="应用截图1" onclick="openLightbox(this)">
                    </div>
                    <div class="screenshot-item">
                        <img src="static/images/2.png" alt="应用截图2" onclick="openLightbox(this)">
                    </div>
                    <div class="screenshot-item">
                        <img src="static/images/3.png" alt="应用截图3" onclick="openLightbox(this)">
                    </div>
                </div>
            </div>

            <!-- 简介 -->
            <div class="app-description">
                <h3>简介</h3>
                <p>歪歪漫画是一款超省流量的漫画阅读app，这款软件不仅能为你提供漫画阅读实时订阅服务，更可以让你在这款软件中享受到根据你的喜好为你提供个性化的主页的便捷！为你提供超多优质漫画资源，海量正版全新漫画应有尽有，歪歪漫画app还有人性化的阅读模式，最新最全的漫画实时更新，使用方便的阅读设置。软件支持离线下载功能，随时随地想看就看，为用户量身打造。是一款袖珍版的全网漫画免费看的软件，拥有最齐全、最热门的韩漫、日漫、国产漫画等，能满足你所有漫画阅读需求。</p>
            </div>
        </section>

        <!-- 评论内容 -->
        <section class="tab-content" id="reviews-content">
            <div class="reviews-header">
                <h3>用户评论</h3>
            </div>
            <div class="reviews-list" id="reviewsList">
                <!-- 评论内容将通过JavaScript动态加载 -->
            </div>
        </section>
    </div>

    <!-- 图片放大查看模态框 -->
    <div class="lightbox" id="lightbox" onclick="closeLightbox()">
        <div class="lightbox-content">
            <img src="" alt="放大图片" id="lightboxImage">
            <button class="lightbox-close" onclick="closeLightbox()">×</button>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <p>正在准备下载...</p>
    </div>

    <script src="static/js/main.js"></script>
</body>
</html>
