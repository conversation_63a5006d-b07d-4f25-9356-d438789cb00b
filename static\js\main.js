// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    setupTabNavigation();
    loadReviews();
    setupImageLightbox();
    setupTouchEvents();
}

// 设置标签页导航
function setupTabNavigation() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');

    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab + '-content').classList.add('active');
        });
    });
}

// 加载用户评论
function loadReviews() {
    const reviewsList = document.getElementById('reviewsList');
    
    // 模拟评论数据
    const reviews = [
        {
            user: "漫***迷",
            rating: 5,
            text: "歪歪漫画资源太丰富了！最新的韩漫日漫都能找到，画质超清晰！"
        },
        {
            user: "追***党",
            rating: 5,
            text: "用了半年了，从来没有卡顿过，阅读很流畅，更新也很及时。"
        },
        {
            user: "二***控",
            rating: 5,
            text: "超省流量的漫画阅读神器，真的是良心应用，强烈推荐！"
        },
        {
            user: "漫***者",
            rating: 5,
            text: "界面简洁，操作方便，找漫画很容易，是我手机必备的阅读应用。"
        },
        {
            user: "漫***狂",
            rating: 5,
            text: "歪歪漫画真的太棒了！韩漫、日漫、国产漫画应有尽有，看漫神器！"
        },
        {
            user: "读***客",
            rating: 5,
            text: "阅读速度快，离线下载功能很实用，随时随地都能看，非常方便。"
        },
        {
            user: "宅***家",
            rating: 5,
            text: "免费看高清漫画，还有个性化主页，体验比付费平台还好！"
        },
        {
            user: "漫***控",
            rating: 5,
            text: "资源更新快，新作品很快就能看到，省流量功能很棒。"
        }
    ];
    
    // 生成评论HTML - 显示8条
    reviews.forEach(review => {
        const reviewElement = createReviewElement(review);
        reviewsList.appendChild(reviewElement);
    });
}

// 创建评论元素
function createReviewElement(review) {
    const reviewDiv = document.createElement('div');
    reviewDiv.className = 'review-item';

    const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);

    reviewDiv.innerHTML = `
        <div class="review-header">
            <span class="review-user">${review.user}</span>
            <span class="review-rating">${stars}</span>
        </div>
        <div class="review-text">${review.text}</div>
    `;

    return reviewDiv;
}

// 设置图片灯箱效果
function setupImageLightbox() {
    const screenshots = document.querySelectorAll('.screenshot-item img');
    
    screenshots.forEach(img => {
        img.addEventListener('click', function() {
            openLightbox(this);
        });
    });
}

// 图片放大查看功能
function openLightbox(img) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    
    if (!lightbox) {
        createLightbox();
        return openLightbox(img);
    }
    
    lightboxImage.src = img.src;
    lightboxImage.alt = img.alt;
    lightbox.classList.add('active');
    
    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

// 创建灯箱元素
function createLightbox() {
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.id = 'lightbox';
    lightbox.onclick = closeLightbox;
    
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="" alt="放大图片" id="lightboxImage">
            <button class="lightbox-close" onclick="closeLightbox()">×</button>
        </div>
    `;
    
    document.body.appendChild(lightbox);
}

// 关闭图片放大查看
function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    if (lightbox) {
        lightbox.classList.remove('active');
    }
    
    // 恢复背景滚动
    document.body.style.overflow = 'auto';
}

// 处理下载功能
async function handleDownload() {
    const loadingElement = document.getElementById('loading');

    try {
        // 立即获取下载地址，避免异步延迟导致Safari阻止弹窗
        let downloadUrl;

        try {
            // 尝试读取下载地址文件
            downloadUrl = await readDownloadUrl();
        } catch (error) {
            console.log('使用默认下载地址');
            // 使用默认下载地址
            downloadUrl = 'https://xzdz.tkbt.cc/?name=413-waiwai';
        }

        // 显示加载提示
        if (!loadingElement) {
            createLoadingElement();
        }
        document.getElementById('loading').classList.add('active');

        // 立即跳转下载，避免Safari的弹窗限制
        if (downloadUrl) {
            // 检测是否为移动设备，特别是iOS
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isIOS || isMobile) {
                // 移动设备使用location.href，更可靠
                window.location.href = downloadUrl;
            } else {
                // 桌面设备使用window.open
                window.open(downloadUrl, '_blank');
            }
        } else {
            alert('暂时无法获取下载地址，请稍后再试。');
        }

        // 短暂延迟后隐藏加载提示
        setTimeout(() => {
            if (document.getElementById('loading')) {
                document.getElementById('loading').classList.remove('active');
            }
        }, 800);

    } catch (error) {
        console.error('下载失败:', error);
        if (document.getElementById('loading')) {
            document.getElementById('loading').classList.remove('active');
        }
        alert('下载失败，请检查网络连接后重试。');
    }
}

// 创建加载提示元素
function createLoadingElement() {
    const loading = document.createElement('div');
    loading.className = 'loading';
    loading.id = 'loading';
    
    loading.innerHTML = `
        <div class="loading-spinner"></div>
        <p>正在准备下载...</p>
    `;
    
    document.body.appendChild(loading);
}

// 读取下载地址文件
async function readDownloadUrl() {
    try {
        // 添加超时控制，避免长时间等待
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

        const response = await fetch('baiduyu.txt', {
            signal: controller.signal,
            cache: 'no-cache' // 避免缓存问题
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const url = await response.text();
            const trimmedUrl = url.trim();
            // 验证URL格式
            if (trimmedUrl && (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://'))) {
                return trimmedUrl;
            } else {
                throw new Error('下载地址格式无效');
            }
        } else {
            throw new Error(`HTTP错误: ${response.status}`);
        }
    } catch (error) {
        console.error('读取下载地址失败:', error);
        // 返回默认下载地址
        return 'https://xzdz.tkbt.cc/?name=413-waiwai';
    }
}

// 设置触摸事件（移动端滑动支持）
function setupTouchEvents() {
    const screenshotsContainer = document.querySelector('.screenshots-wrapper');
    
    if (screenshotsContainer) {
        let touchStartX = 0;
        let touchEndX = 0;
        
        screenshotsContainer.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        }, { passive: true });
        
        screenshotsContainer.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].clientX;
            handleSwipe();
        }, { passive: true });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const swipeDistance = touchStartX - touchEndX;
            
            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    // 向左滑动
                    screenshotsContainer.scrollLeft += 200;
                } else {
                    // 向右滑动
                    screenshotsContainer.scrollLeft -= 200;
                }
            }
        }
    }
}

// 设置键盘事件
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.classList.contains('active')) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
});

// 图片加载错误处理
document.addEventListener('error', function(e) {
    if (e.target.tagName === 'IMG') {
        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+PC9zdmc+';
        e.target.alt = '图片加载失败';
    }
}, true);
