// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    setupTabNavigation();
    loadReviews();
    setupImageLightbox();
    setupTouchEvents();
}

// 设置标签页导航
function setupTabNavigation() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');

    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab + '-content').classList.add('active');
        });
    });
}

// 加载用户评论
function loadReviews() {
    const reviewsList = document.getElementById('reviewsList');
    
    // 模拟评论数据
    const reviews = [
        {
            user: "漫***迷",
            rating: 5,
            text: "歪歪漫画资源太丰富了！最新的韩漫日漫都能找到，画质超清晰！"
        },
        {
            user: "追***党",
            rating: 5,
            text: "用了半年了，从来没有卡顿过，阅读很流畅，更新也很及时。"
        },
        {
            user: "二***控",
            rating: 5,
            text: "超省流量的漫画阅读神器，真的是良心应用，强烈推荐！"
        },
        {
            user: "漫***者",
            rating: 5,
            text: "界面简洁，操作方便，找漫画很容易，是我手机必备的阅读应用。"
        },
        {
            user: "漫***狂",
            rating: 5,
            text: "歪歪漫画真的太棒了！韩漫、日漫、国产漫画应有尽有，看漫神器！"
        },
        {
            user: "读***客",
            rating: 5,
            text: "阅读速度快，离线下载功能很实用，随时随地都能看，非常方便。"
        },
        {
            user: "宅***家",
            rating: 5,
            text: "免费看高清漫画，还有个性化主页，体验比付费平台还好！"
        },
        {
            user: "漫***控",
            rating: 5,
            text: "资源更新快，新作品很快就能看到，省流量功能很棒。"
        }
    ];
    
    // 生成评论HTML - 显示8条
    reviews.forEach(review => {
        const reviewElement = createReviewElement(review);
        reviewsList.appendChild(reviewElement);
    });
}

// 创建评论元素
function createReviewElement(review) {
    const reviewDiv = document.createElement('div');
    reviewDiv.className = 'review-item';

    const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);

    reviewDiv.innerHTML = `
        <div class="review-header">
            <span class="review-user">${review.user}</span>
            <span class="review-rating">${stars}</span>
        </div>
        <div class="review-text">${review.text}</div>
    `;

    return reviewDiv;
}

// 设置图片灯箱效果
function setupImageLightbox() {
    const screenshots = document.querySelectorAll('.screenshot-item img');
    
    screenshots.forEach(img => {
        img.addEventListener('click', function() {
            openLightbox(this);
        });
    });
}

// 图片放大查看功能
function openLightbox(img) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    
    if (!lightbox) {
        createLightbox();
        return openLightbox(img);
    }
    
    lightboxImage.src = img.src;
    lightboxImage.alt = img.alt;
    lightbox.classList.add('active');
    
    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

// 创建灯箱元素
function createLightbox() {
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.id = 'lightbox';
    lightbox.onclick = closeLightbox;
    
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="" alt="放大图片" id="lightboxImage">
            <button class="lightbox-close" onclick="closeLightbox()">×</button>
        </div>
    `;
    
    document.body.appendChild(lightbox);
}

// 关闭图片放大查看
function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    if (lightbox) {
        lightbox.classList.remove('active');
    }
    
    // 恢复背景滚动
    document.body.style.overflow = 'auto';
}

// 处理下载功能
async function handleDownload() {
    const loadingElement = document.getElementById('loading');
    
    try {
        // 显示加载提示
        if (!loadingElement) {
            createLoadingElement();
        }
        document.getElementById('loading').classList.add('active');
        
        // 读取下载地址文件
        const downloadUrl = await readDownloadUrl();
        
        // 模拟加载时间
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 隐藏加载提示
        document.getElementById('loading').classList.remove('active');
        
        if (downloadUrl) {
            // 跳转到下载地址
            window.open(downloadUrl, '_blank');
        } else {
            alert('暂时无法获取下载地址，请稍后再试。');
        }
        
    } catch (error) {
        console.error('下载失败:', error);
        if (document.getElementById('loading')) {
            document.getElementById('loading').classList.remove('active');
        }
        alert('下载失败，请检查网络连接后重试。');
    }
}

// 创建加载提示元素
function createLoadingElement() {
    const loading = document.createElement('div');
    loading.className = 'loading';
    loading.id = 'loading';
    
    loading.innerHTML = `
        <div class="loading-spinner"></div>
        <p>正在准备下载...</p>
    `;
    
    document.body.appendChild(loading);
}

// 读取下载地址文件
async function readDownloadUrl() {
    try {
        const response = await fetch('baiduyu.txt');
        if (response.ok) {
            const url = await response.text();
            return url.trim();
        } else {
            throw new Error('无法读取下载地址文件');
        }
    } catch (error) {
        console.error('读取下载地址失败:', error);
        // 返回默认下载地址
        return 'https://cci.iiorr.com/?name=105-jianpian';
    }
}

// 设置触摸事件（移动端滑动支持）
function setupTouchEvents() {
    const screenshotsContainer = document.querySelector('.screenshots-wrapper');
    
    if (screenshotsContainer) {
        let touchStartX = 0;
        let touchEndX = 0;
        
        screenshotsContainer.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        }, { passive: true });
        
        screenshotsContainer.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].clientX;
            handleSwipe();
        }, { passive: true });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const swipeDistance = touchStartX - touchEndX;
            
            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    // 向左滑动
                    screenshotsContainer.scrollLeft += 200;
                } else {
                    // 向右滑动
                    screenshotsContainer.scrollLeft -= 200;
                }
            }
        }
    }
}

// 设置键盘事件
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.classList.contains('active')) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
});

// 图片加载错误处理
document.addEventListener('error', function(e) {
    if (e.target.tagName === 'IMG') {
        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+PC9zdmc+';
        e.target.alt = '图片加载失败';
    }
}, true);
