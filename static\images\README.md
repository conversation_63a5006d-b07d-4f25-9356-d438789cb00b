# 图片资源说明

请将以下图片文件放入此目录：

## 必需的图片文件

### 1. app-logo.png
- **用途**: APP图标
- **建议尺寸**: 512x512px
- **格式**: PNG（支持透明背景）
- **要求**: 高质量，清晰的APP图标

### 2. 应用截图 (screenshot1.jpg - screenshot5.jpg)
- **用途**: 应用功能展示截图
- **建议尺寸**: 1080x1920px (9:16比例)
- **格式**: JPG 或 PNG
- **数量**: 至少3张，最多5张
- **要求**: 
  - 展示APP的主要功能界面
  - 高清晰度
  - 统一的设备边框样式（可选）

## 可选的图片文件

### 3. placeholder.png
- **用途**: 图片加载失败时的占位图
- **建议尺寸**: 300x300px
- **格式**: PNG
- **内容**: 简单的占位图标或文字

## 图片优化建议

1. **文件大小控制**
   - APP图标: < 100KB
   - 截图: < 500KB 每张
   - 使用适当的压缩比例

2. **格式选择**
   - 图标: PNG（支持透明）
   - 截图: JPG（文件更小）
   - 简单图形: SVG（可选）

3. **响应式考虑**
   - 提供不同分辨率版本（可选）
   - 使用 srcset 属性（高级功能）

## 在线图片资源

如果暂时没有图片，可以使用以下占位服务：

- APP图标: `https://via.placeholder.com/512x512/667eea/ffffff?text=APP`
- 截图: `https://via.placeholder.com/1080x1920/f8f9fa/333333?text=Screenshot`

## 图片命名规范

请严格按照以下命名：
- `app-logo.png`
- `screenshot1.jpg`
- `screenshot2.jpg`
- `screenshot3.jpg`
- `screenshot4.jpg`
- `screenshot5.jpg`
- `placeholder.png` (可选)

## 注意事项

1. 确保图片文件名与HTML中的引用完全一致
2. 建议使用小写文件名
3. 避免使用中文文件名
4. 检查图片是否正确显示在不同设备上
