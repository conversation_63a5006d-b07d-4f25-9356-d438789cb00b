/* 响应式设计 */

/* 大屏设备 (768px 及以上) */
@media (min-width: 768px) {
    .container {
        max-width: 600px;
        padding: 40px;
    }

    .app-icon img {
        width: 100px;
        height: 100px;
    }

    .app-name {
        font-size: 28px;
    }

    .screenshot-item {
        width: 160px;
        height: 280px;
    }
}

/* 小屏设备 (480px 及以下) */
@media (max-width: 480px) {
    .container {
        padding: 16px;
    }

    .app-header {
        margin-bottom: 16px;
    }

    .app-icon {
        margin-right: 12px;
    }

    .app-icon img {
        width: 70px;
        height: 70px;
        border-radius: 14px;
    }



    .app-name {
        font-size: 20px;
        margin-bottom: 6px;
    }

    .app-rating {
        gap: 8px;
        margin-bottom: 6px;
    }

    .rating-score,
    .download-count {
        font-size: 13px;
    }

    .app-category {
        font-size: 11px;
        padding: 3px 6px;
    }

    .download-section {
        margin-bottom: 20px;
    }

    .download-btn {
        padding: 10px 20px;
        font-size: 15px;
        border-radius: 20px;
    }

    .tab-navigation {
        margin-bottom: 16px;
    }

    .tab-item {
        padding: 10px 0;
        font-size: 15px;
    }

    .screenshots-container {
        margin-bottom: 24px;
    }

    .screenshot-item {
        width: 120px;
        height: 210px;
        border-radius: 10px;
    }

    .app-description {
        margin-bottom: 24px;
    }

    .app-description h3 {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .app-description p {
        font-size: 13px;
    }

    .reviews-header h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .review-item {
        padding: 12px 0;
    }

    .review-user {
        font-size: 13px;
    }

    .review-rating {
        font-size: 11px;
    }

    .review-text {
        font-size: 13px;
    }

    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
    }

    .lightbox-close {
        top: -35px;
        font-size: 1.5rem;
        width: 35px;
        height: 35px;
    }
}

/* 超小屏设备 (360px 及以下) */
@media (max-width: 360px) {
    .container {
        padding: 12px;
    }

    .app-icon img {
        width: 60px;
        height: 60px;
        border-radius: 12px;
    }

    .app-name {
        font-size: 18px;
    }

    .rating-score,
    .download-count {
        font-size: 12px;
    }

    .screenshot-item {
        width: 100px;
        height: 180px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .screenshot-item:hover {
        transform: none;
    }

    .download-btn:hover {
        background: #007aff;
        transform: none;
    }

    /* 增大触摸目标 */
    .tab-item {
        padding: 14px 0;
    }

    .lightbox-close {
        width: 44px;
        height: 44px;
        font-size: 1.8rem;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
    .container {
        padding: 16px;
    }

    .app-header {
        margin-bottom: 16px;
    }

    .download-section {
        margin-bottom: 16px;
    }

    .tab-navigation {
        margin-bottom: 16px;
    }

    .screenshots-container {
        margin-bottom: 20px;
    }

    .app-description {
        margin-bottom: 20px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1c1c1e;
    }

    .container {
        background: #2c2c2e;
        color: #ffffff;
    }

    .app-name {
        color: #ffffff;
    }

    .rating-score,
    .download-count {
        color: #8e8e93;
    }

    .tab-item {
        color: #8e8e93;
    }

    .tab-item.active {
        color: #007aff;
    }

    .app-description h3 {
        color: #ffffff;
    }

    .app-description p {
        color: #8e8e93;
    }

    .reviews-header h3 {
        color: #ffffff;
    }

    .review-user {
        color: #ffffff;
    }

    .review-text {
        color: #8e8e93;
    }

    .review-item {
        border-bottom-color: #3a3a3c;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-spinner {
        animation: none;
        border: 4px solid white;
    }
}
